class NotificationMailerPreview < ActionMailer::Preview
  
  # http://192.168.1.51:5000/rails/mailers/notification_mailer/access_request_notification
  def access_request_notification
    project = Project.last
    user = User.last
    NotificationMailer.access_request_notification(project, user)
  end
  
  # http://192.168.1.51:5000/rails/mailers/notification_mailer/admin_project_notification
  def admin_project_notification
    project = Project.last
    admin = User.where(role: 'super_boss').first
    current_user = User.last
    NotificationMailer.admin_project_notification(project, admin, current_user)
  end
  
  # http://192.168.1.51:5000/rails/mailers/notification_mailer/new_project_notification
  def new_project_notification
    project = Project.first
    user = User.last
    NotificationMailer.new_project_notification(project, user)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/approved_access_notification
  def approved_access_notification
    project = Project.last
    user = User.last
    current_user = User.last
    NotificationMailer.approved_access_notification(project, user, current_user)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/user_approval_request_notification
  def user_approval_request_notification
    user = User.includes(:user_profile).last
    NotificationMailer.user_approval_request_notification(user)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/user_pending_approval_notification
  def user_pending_approval_notification
    user = User.includes(:user_profile).last
    NotificationMailer.user_pending_approval_notification(user)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/user_approval_notification
  def user_approval_notification
    # Create a user that matches the current preview locale
    user = User.includes(:user_profile).last
    
    # DEBUG: Log what's happening
    Rails.logger.info "🔍 [PREVIEW DEBUG] I18n.locale = #{I18n.locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG] User original language = #{user&.user_profile&.default_language}"
    
    # For preview purposes, temporarily override the user's language preference
    # to match the Rails preview locale parameter
    if user&.user_profile
      original_language = user.user_profile.default_language
      user.user_profile.default_language = I18n.locale.to_s
      
      Rails.logger.info "🔍 [PREVIEW DEBUG] Set user language to = #{user.user_profile.default_language}"
      
      mail = NotificationMailer.user_approval_notification(user)
      
      Rails.logger.info "🔍 [PREVIEW DEBUG] Generated subject = #{mail.subject}"
      
      # Restore original language (important for preview, doesn't affect DB)
      user.user_profile.default_language = original_language
      
      return mail
    end
    
    NotificationMailer.user_approval_notification(user)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/daily_new_projects_digest.txt?locale=en
  def daily_new_projects_digest
    user = User.includes(:user_profile).last
    new_projects_count = 5 # Example count for preview
    
    # Get the locale from params if provided, otherwise use current locale
    preview_locale = params[:locale] || I18n.locale.to_s
    
    Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] params[:locale] = #{params[:locale]}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] I18n.locale = #{I18n.locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] preview_locale = #{preview_locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] User original language = #{user&.user_profile&.default_language}"
    
    # For preview purposes, temporarily override the user's language preference
    # to match the Rails preview locale parameter
    if user&.user_profile
      original_language = user.user_profile.default_language
      user.user_profile.default_language = preview_locale
      
      Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] Set user language to = #{user.user_profile.default_language}"
      
      mail = NotificationMailer.daily_new_projects_digest(user, new_projects_count)
      
      Rails.logger.info "🔍 [PREVIEW DEBUG - DAILY] Generated subject = #{mail.subject}"
      
      # Restore original language (important for preview, doesn't affect DB)
      user.user_profile.default_language = original_language
      
      return mail
    end
    
    NotificationMailer.daily_new_projects_digest(user, new_projects_count)
  end

  # http://192.168.1.51:5000/rails/mailers/notification_mailer/weekly_new_projects_digest.html?locale=sk
  def weekly_new_projects_digest
    user = User.includes(:user_profile).last
    new_projects_count = 12 # Example count for preview
    
    # Get the locale from params if provided, otherwise use current locale
    preview_locale = params[:locale] || I18n.locale.to_s
    
    Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] params[:locale] = #{params[:locale]}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] I18n.locale = #{I18n.locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] preview_locale = #{preview_locale}"
    Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] User original language = #{user&.user_profile&.default_language}"
    
    # For preview purposes, temporarily override the user's language preference
    # to match the Rails preview locale parameter
    if user&.user_profile
      original_language = user.user_profile.default_language
      user.user_profile.default_language = preview_locale
      
      Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] Set user language to = #{user.user_profile.default_language}"
      
      mail = NotificationMailer.weekly_new_projects_digest(user, new_projects_count)
      
      Rails.logger.info "🔍 [PREVIEW DEBUG - WEEKLY] Generated subject = #{mail.subject}"
      
      # Restore original language (important for preview, doesn't affect DB)
      user.user_profile.default_language = original_language
      
      return mail
    end
    
    NotificationMailer.weekly_new_projects_digest(user, new_projects_count)
  end

end 